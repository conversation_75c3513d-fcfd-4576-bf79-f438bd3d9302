public without sharing class CaseRulesAfter {

    public static void processNewInformationOnCase(List<Case> cases) {
        if (cases == null || cases.isEmpty()) {
            return;
        }

        List<Case> candidateCases = getCandidateCases(cases);
        if (candidateCases.isEmpty() || !Schema.sObjectType.User.isAccessible()) {
            return;
        }

        Map<Id, User> createdByUsers = getCreatedByUsers(candidateCases);
        List<Case> casesToUpdate = buildCasesToUpdate(candidateCases, createdByUsers);

        performBulkUpdate(casesToUpdate);
    }

    private static List<Case> getCandidateCases(List<Case> cases) {
        Set<Id> createdByIds = new Set<Id>();
        List<Case> candidateCases = new List<Case>();
        for (Case c : cases) {
            if (c.Id != null) {
                candidateCases.add(c);
            }
        }
        return candidateCases;
    }

    private static Map<Id, User> getCreatedByUsers(List<Case> candidateCases) {
        Set<Id> createdByIds = new Set<Id>();
        for (Case c : candidateCases) {
            if (c.CreatedById != null) {
                createdByIds.add(c.CreatedById);
            }
        }
        Map<Id, User> createdByUsers = new Map<Id, User>();
        if (!createdByIds.isEmpty()) {
            for (User user : [SELECT Id, Name FROM User WHERE Id IN :createdByIds]) {
                createdByUsers.put(user.Id, user);
            }
        }
        return createdByUsers;
    }

    private static List<Case> buildCasesToUpdate(List<Case> candidateCases, Map<Id, User> createdByUsers) {
        List<Case> casesToUpdate = new List<Case>();
        for (Case originalCase : candidateCases) {
            Case caseUpdate = new Case(Id = originalCase.Id);
            boolean needsUpdate = false;

            if (String.isBlank(originalCase.ExternalId__c)) {
                caseUpdate.ExternalId__c = 'SF' + originalCase.Id;
                needsUpdate = true;
            }

            if (String.isBlank(originalCase.SourceSystem__c)) {
                String newSourceSystem = calculateSourceSystem(originalCase, createdByUsers);
                if (newSourceSystem != null) {
                    caseUpdate.SourceSystem__c = newSourceSystem;
                    needsUpdate = true;
                }
            }

            if (needsUpdate) {
                casesToUpdate.add(caseUpdate);
            }
        }
        return casesToUpdate;
    }
    
    private static String calculateSourceSystem(Case originalCase, Map<Id, User> createdByUsers) {
        if (String.isNotBlank(originalCase.DrCreatoDaUtente__c)) {
            return originalCase.DrCreatoDaUtente__c;
        }
        else if (String.isNotBlank(originalCase.DrCreatoDaNominativo__c)) {
            return originalCase.DrCreatoDaNominativo__c;
        }
        else {
            User createdByUser = createdByUsers.get(originalCase.CreatedById);
            if (createdByUser != null && createdByUser.Name != null && createdByUser.Name.toLowerCase().contains('mulesoft')) {
                return 'SEXT';
            } else {
                return 'SF';
            }
        }
    }
    
    private static void performBulkUpdate(List<Case> casesToUpdate) {
        if (casesToUpdate.isEmpty()) {
            return;
        }
        
        try {
            System.debug('Aggiornamento bulk di ' + casesToUpdate.size() + ' Case');
            Database.SaveResult[] results = Database.update(casesToUpdate, false);
            Integer successCount = 0;
            for (Integer i = 0; i < results.size(); i++) {
                if (results[i].isSuccess()) {
                    successCount++;
                } else {
                    System.debug('Errore aggiornamento Case ID ' + casesToUpdate[i].Id + ': ' + results[i].getErrors());
                }
            }
            System.debug('Aggiornamento completato: ' + successCount + '/' + casesToUpdate.size() + ' Case processati');    
        } catch (Exception ex) {
            System.debug('Eccezione in performBulkUpdate: ' + ex.getMessage());
        }
    }
}