@isTest
private class CaseRulesAfterTest {

    @isTest
    static void testProcessNewInformationOnCaseWithRunAs() {

        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Alias = 'tuser',
            Email = '<EMAIL>',
            Username = '<EMAIL>.' + System.currentTimeMillis(),
            CommunityNickname = 'testuser',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1].Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            IdAzienda__c = testAccount.Id
        );
        insert testUser;

        System.runAs(testUser) {
            Case testCase = new Case(
                Subject = 'Test Case',
                ExternalId__c = null,
                SourceSystem__c = null,
                DrCreatoDaUtente__c = null,
                DrCreatoDaNominativo__c = null
            );
            insert testCase;

            List<Case> cases = [SELECT Id, CreatedById, ExternalId__c, SourceSystem__c, DrCreatoDaUtente__c, DrCreatoDaNominativo__c FROM Case WHERE Id = :testCase.Id];

            Test.startTest();
            CaseRulesAfter.processNewInformationOnCase(cases);
            Test.stopTest();

            Case updatedCase = [SELECT ExternalId__c, SourceSystem__c FROM Case WHERE Id = :testCase.Id];
            System.assertEquals('SF' + testCase.Id, updatedCase.ExternalId__c, 'ExternalId__c should be SF + Case Id');
            System.assertEquals('SF', updatedCase.SourceSystem__c, 'SourceSystem__c should be SF');
        }
    }
}