/**
 * @File Name         : urcs_SendEmail_Test.cls
 * @Description       : Test class for urcs_SendEmail
 * <AUTHOR> ACN DEV TEAM
 * @Group             : 
 * @Last Modified On  : 03-09-2025
 * @Last Modified By  : ACN DEV TEAM
**/
@isTest
public class urcs_SendEmail_Test {
    
    @TestSetup
    static void makeData() {
        // Create test data
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        Contact testContact = new Contact(
            FirstName = 'Test',
            LastName = 'Contact',
            Email = '<EMAIL>',
            AccountId = testAccount.Id
        );
        insert testContact;

        Case testCase = new Case(
            Subject = 'Test Case',
            ContactId = testContact.Id,
            AccountId = testAccount.Id
        );
        insert testCase;
    }
    
    @isTest
    static void testSendEmailToTargetAddressSuccess() {
        // Get test data
        Contact testContact = [SELECT Id FROM Contact LIMIT 1];
        Case testCase = [SELECT Id FROM Case LIMIT 1];

        Test.startTest();
        urcs_SendEmail.sendEmailToTargetAddress(
            'TestTemplate',
            testContact.Id,
            testCase.Id,
            '<EMAIL>'
        );
        Test.stopTest();
    }
    
    @isTest
    static void testSendEmailToTargetAddressNullParams() {
        Test.startTest();
        urcs_SendEmail.sendEmailToTargetAddress(null, null, null, null);
        Test.stopTest();
    }
    
    @isTest
    static void testSendEmailToTargetAddressInvalidTemplate() {
        Contact testContact = [SELECT Id FROM Contact LIMIT 1];
        Case testCase = [SELECT Id FROM Case LIMIT 1];
        
        Test.startTest();
        urcs_SendEmail.sendEmailToTargetAddress(
            'NonExistentTemplate',
            testContact.Id,
            testCase.Id,
            '<EMAIL>'
        );
        Test.stopTest();
    }
    
    @isTest
    static void testSendEmailToTargetAddressInvocableSuccess() {
        Contact testContact = [SELECT Id FROM Contact LIMIT 1];
        Case testCase = [SELECT Id FROM Case LIMIT 1];

        urcs_SendEmail.InvocableParameters params = new urcs_SendEmail.InvocableParameters();
        params.whoId = testContact.Id;
        params.whatId = testCase.Id;
        params.templateName = 'TestTemplate';
        params.targetAddress = '<EMAIL>';

        List<urcs_SendEmail.InvocableParameters> paramsList = new List<urcs_SendEmail.InvocableParameters>();
        paramsList.add(params);

        Test.startTest();
        urcs_SendEmail.sendEmailToTargetAddressInvocable(paramsList);
        Test.stopTest();
    }
    
    @isTest
    static void testSendEmailToTargetAddressInvocableEmptyList() {
        List<urcs_SendEmail.InvocableParameters> emptyList = new List<urcs_SendEmail.InvocableParameters>();
        
        Test.startTest();
        urcs_SendEmail.sendEmailToTargetAddressInvocable(emptyList);
        Test.stopTest();
    }
}
