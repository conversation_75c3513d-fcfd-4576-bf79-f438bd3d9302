<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Account.Presa_appuntamento</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CustomButton.Account.Crea_Report</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>recordId</name>
                </componentInstanceProperties>
                <componentName>cfAnagraficaCloneForUnipolSai</componentName>
                <identifier>c_cfAnagraficaCloneForUnipolSai</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>Anagrafica</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-7cd015fb-d063-4a68-abd0-6a125ffe6322</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>AnagraficaUniSalute</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-866c9c28-de2a-400b-9558-d3a50d579de9</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Opportunities</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-19cc3f6d-7506-49b7-ac52-dc7d7e423c38</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Opportunities</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b1lojt5dlwv</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-7cd015fb-d063-4a68-abd0-6a125ffe6322</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Unipol</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUnipolSai}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-866c9c28-de2a-400b-9558-d3a50d579de9</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>UniSalute</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUniSalute}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-19cc3f6d-7506-49b7-ac52-dc7d7e423c38</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>UnipolRental</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab8</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUnipolRental}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b1lojt5dlwv</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>UnipolTech</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab9</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUnipolTech}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-3b1523dd-2e7a-4558-ae32-91d8c2a56dd3</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-3b1523dd-2e7a-4558-ae32-91d8c2a56dd3</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset2</identifier>
            </componentInstance>
        </itemInstances>
        <name>leftcol</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniProdottiPressoAgenziaSelector</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniProdottiAssicurativi</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniProdottiAssicurativiAltraAgenzia</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard12</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniProdottiNonAssicurativi</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard8</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniTitoliInScadenza</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniContenziosi</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard10</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniSinistri</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard11</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniContenziosi</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard13</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e4847245-9c5c-466b-b05b-0a6c79b41740</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>exposedAttributes</name>
                    <value>{&quot;UserId&quot;:&quot;&quot;}</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>UniDocumentazione</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9a7614f2-9c45-48f1-b23b-6aac8d8b68cc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>builder_industries_insurance:lifeEvents</componentName>
                <identifier>builder_industries_insurance_lifeEvents</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SS_DettagliSocioEconomici</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-84f0b58f-a6cb-4580-9d18-7c96b3f95cfb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>showLegacyActivityComposer</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_activities:activityPanel</componentName>
                <identifier>runtime_sales_activities_activityPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-asn2n301fze</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>showLegacyActivityComposer</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_activities:activityPanel</componentName>
                <identifier>runtime_sales_activities_activityPanel2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b5b94027-69f6-4403-a64e-41ed2b5c0a12</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e4847245-9c5c-466b-b05b-0a6c79b41740</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Prodotti</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-9a7614f2-9c45-48f1-b23b-6aac8d8b68cc</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Documentazione</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab4</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$User.IsActive}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>false</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-84f0b58f-a6cb-4580-9d18-7c96b3f95cfb</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Info Aggiuntive</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-asn2n301fze</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Timeline</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b5b94027-69f6-4403-a64e-41ed2b5c0a12</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Reg Comunicazioni</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab7</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-331840e9-cf2a-42cf-b75b-13d39db21bd8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>stage</name>
                    <value>Aperte</value>
                </componentInstanceProperties>
                <componentName>opportunityRelatedList</componentName>
                <identifier>c_opportunityRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-c1c4760f-82a9-475e-97de-00d14fce2185</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>stage</name>
                    <value>Chiuse</value>
                </componentInstanceProperties>
                <componentName>opportunityRelatedList</componentName>
                <identifier>c_opportunityRelatedList2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-0d0b60be-8e21-4295-b8f1-021573a7b21b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c1c4760f-82a9-475e-97de-00d14fce2185</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Trattative Aperte</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab10</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0d0b60be-8e21-4295-b8f1-021573a7b21b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Trattative Chiuse</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab11</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-abc82a33-9ae5-493b-bd72-90bd16a7ea6f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>stato</name>
                    <value>Aperte</value>
                </componentInstanceProperties>
                <componentName>caseRelatedList</componentName>
                <identifier>c_caseRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-045a9f6c-5768-47e7-ad3d-fffb1a44d59b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>stato</name>
                    <value>Chiuse</value>
                </componentInstanceProperties>
                <componentName>caseRelatedList</componentName>
                <identifier>c_caseRelatedList2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-c6ea9b62-9881-4148-a443-7fbadb4deed4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-045a9f6c-5768-47e7-ad3d-fffb1a44d59b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Attività Aperte</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab12</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c6ea9b62-9881-4148-a443-7fbadb4deed4</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Attività Chiuse</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab13</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-009fd004-cda1-44c9-b2ae-ee266d72d136</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-331840e9-cf2a-42cf-b75b-13d39db21bd8</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-abc82a33-9ae5-493b-bd72-90bd16a7ea6f</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset3</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUniSalute}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-009fd004-cda1-44c9-b2ae-ee266d72d136</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset4</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUniSalute}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.TabsUnipolSai}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SS_Box_Task_Contatto</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard3</identifier>
            </componentInstance>
        </itemInstances>
        <name>rightcol</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Business Record Page</masterLabel>
    <sobjectType>Account</sobjectType>
    <template>
        <name>flexipage:recordHomeTwoColEqualHeaderTemplateDesktop</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
